package acmex

import (
	"context"
	"errors"
	"fmt"
	"log/slog"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/go-acme/lego/v4/certcrypto"
	"github.com/go-acme/lego/v4/certificate"
	"github.com/go-acme/lego/v4/challenge/dns01"
	"github.com/go-acme/lego/v4/lego"
	"github.com/go-acme/lego/v4/providers/dns/tencentcloud"
	"github.com/go-acme/lego/v4/registration"
)

type Config struct {
	// 必填：账户邮箱（用于 ACME 注册）
	Email string

	// 可选：数据目录（账户与证书落盘）
	DataDir string

	// 可选：距到期多久开始续期，默认 30d
	RenewBefore time.Duration

	// 可选：ACME 目录地址（默认 Let's Encrypt 生产环境）
	// 例如：LE Staging: https://acme-staging-v02.api.letsencrypt.org/directory
	CADirURL string

	// 可选：证书密钥类型（默认 EC256）
	KeyType certcrypto.KeyType
}

type Manager struct {
	cfg Config

	accMu sync.Mutex
	user  *user // 账户（懒加载）

	locksMu sync.Mutex
	locks   map[string]*sync.Mutex // 每个域名一个互斥锁
}

func DefaultConfig(email, dataDir string) Config {
	return Config{
		Email:       email,
		DataDir:     dataDir,
		RenewBefore: 30 * 24 * time.Hour,
		CADirURL:    "", // 走默认
		KeyType:     certcrypto.EC256,
	}
}

func NewManager(cfg Config) (*Manager, error) {
	if cfg.Email == "" {
		return nil, errors.New("acmex: Email required")
	}
	if cfg.DataDir == "" {
		cfg.DataDir = "./data"
	}
	if cfg.RenewBefore == 0 {
		cfg.RenewBefore = 30 * 24 * time.Hour
	}
	if cfg.KeyType == "" {
		cfg.KeyType = certcrypto.EC256
	}
	if err := os.MkdirAll(cfg.DataDir, 0700); err != nil {
		return nil, err
	}
	return &Manager{
		cfg:   cfg,
		locks: make(map[string]*sync.Mutex),
	}, nil
}

func (m *Manager) lockDomain(d string) func() {
	m.locksMu.Lock()
	l, ok := m.locks[d]
	if !ok {
		l = &sync.Mutex{}
		m.locks[d] = l
	}
	m.locksMu.Unlock()
	l.Lock()
	return func() { l.Unlock() }
}

// GetCertificate 会返回 fullchainPEM 与 privateKeyPEM（均为 PEM）
// 若本地已有且未临期，直接读取返回；否则通过 DNS-01（Tencent Cloud）申请/续期。
func (m *Manager) GetCertificate(ctx context.Context, domain string) (fullchainPEM, privateKeyPEM []byte, err error) {
	domain = strings.TrimSpace(domain)
	if domain == "" {
		return nil, nil, errors.New("acmex: empty domain")
	}
	unlock := m.lockDomain(domain)
	defer unlock()

	slog.Debug("Checking existing certificate",
		slog.String("domain", domain),
		slog.String("data_dir", m.cfg.DataDir))

	// 1) 若已有证书且未临期：直接读取返回
	certPEM, keyPEM, notAfter, err := LoadCert(m.cfg.DataDir, domain)
	if err == nil && !needRenew(time.Now(), notAfter, m.cfg.RenewBefore) {
		slog.Info("Using existing valid certificate",
			slog.String("domain", domain),
			slog.Time("not_after", notAfter),
			slog.Duration("renew_before", m.cfg.RenewBefore))
		return certPEM, keyPEM, nil
	}

	if err != nil {
		slog.Debug("No existing certificate found or failed to load",
			slog.String("domain", domain),
			slog.String("error", err.Error()))
	} else {
		slog.Info("Certificate needs renewal",
			slog.String("domain", domain),
			slog.Time("not_after", notAfter),
			slog.Duration("renew_before", m.cfg.RenewBefore),
			slog.Bool("needs_renew", needRenew(time.Now(), notAfter, m.cfg.RenewBefore)))
	}

	// 2) 准备 lego client（含账户与 DNS-01 提供商）
	slog.Info("Preparing ACME client", slog.String("domain", domain))
	client, err := m.prepareClient()
	if err != nil {
		slog.Error("Failed to prepare ACME client",
			slog.String("domain", domain),
			slog.String("error", err.Error()))
		return nil, nil, err
	}

	// 3) 申请/续期
	slog.Info("Starting certificate issuance/renewal via ACME",
		slog.String("domain", domain),
		slog.String("ca_dir_url", m.cfg.CADirURL))

	req := certificate.ObtainRequest{
		Domains: []string{domain},
		Bundle:  true, // 返回 fullchain
	}
	res, err := client.Certificate.Obtain(req)
	if err != nil {
		slog.Error("ACME certificate obtain failed",
			slog.String("domain", domain),
			slog.String("error", err.Error()))
		return nil, nil, fmt.Errorf("acmex: obtain failed: %w", err)
	}

	slog.Info("Certificate obtained successfully from ACME",
		slog.String("domain", domain),
		slog.Int("cert_size", len(res.Certificate)),
		slog.Int("key_size", len(res.PrivateKey)))

	// 4) 落盘
	slog.Debug("Saving certificate to disk", slog.String("domain", domain))
	if err := SaveCert(m.cfg.DataDir, domain, res.Certificate, res.PrivateKey); err != nil {
		slog.Error("Failed to save certificate",
			slog.String("domain", domain),
			slog.String("error", err.Error()))
		return nil, nil, err
	}

	slog.Info("Certificate saved successfully",
		slog.String("domain", domain),
		slog.String("data_dir", m.cfg.DataDir))

	return res.Certificate, res.PrivateKey, nil
}

// prepareClient 懒加载账户、注册并创建 lego client 与 Tencent Cloud DNS provider。
func (m *Manager) prepareClient() (*lego.Client, error) {
	slog.Debug("Loading or creating ACME account", slog.String("email", m.cfg.Email))
	u, err := m.loadOrCreateAccount()
	if err != nil {
		slog.Error("Failed to load or create ACME account",
			slog.String("email", m.cfg.Email),
			slog.String("error", err.Error()))
		return nil, err
	}

	cfg := lego.NewConfig(u)
	if m.cfg.CADirURL != "" {
		cfg.CADirURL = m.cfg.CADirURL
	}
	cfg.Certificate.KeyType = m.cfg.KeyType

	slog.Debug("Creating ACME client",
		slog.String("ca_dir_url", cfg.CADirURL),
		slog.String("key_type", string(cfg.Certificate.KeyType)))

	client, err := lego.NewClient(cfg)
	if err != nil {
		slog.Error("Failed to create ACME client", slog.String("error", err.Error()))
		return nil, err
	}

	// DNS Provider：Tencent Cloud（读取 TENCENTCLOUD_SECRET_ID/KEY）
	slog.Debug("Initializing TencentCloud DNS provider")
	provider, err := tencentcloud.NewDNSProvider()
	if err != nil {
		slog.Error("Failed to initialize TencentCloud DNS provider", slog.String("error", err.Error()))
		return nil, fmt.Errorf("acmex: tencentcloud provider: %w", err)
	}

	nameservers := []string{"202.46.226.242", "223.5.5.5", "119.29.29.29"}
	slog.Debug("Setting DNS-01 provider with recursive nameservers",
		slog.Any("nameservers", nameservers))

	_ = client.Challenge.SetDNS01Provider(
		provider,
		dns01.CondOption(true, dns01.AddRecursiveNameservers(nameservers)),
	)

	// 账户注册（若尚未）
	if u.Registration == nil {
		slog.Info("Registering new ACME account", slog.String("email", m.cfg.Email))
		reg, err := client.Registration.Register(registration.RegisterOptions{
			TermsOfServiceAgreed: true,
		})
		if err != nil {
			slog.Error("Failed to register ACME account",
				slog.String("email", m.cfg.Email),
				slog.String("error", err.Error()))
			return nil, fmt.Errorf("acmex: register: %w", err)
		}
		u.Registration = reg
		if err := SaveAccount(m.cfg.DataDir, u); err != nil {
			slog.Error("Failed to save ACME account",
				slog.String("email", m.cfg.Email),
				slog.String("error", err.Error()))
			return nil, err
		}
		slog.Info("ACME account registered and saved successfully", slog.String("email", m.cfg.Email))
	} else {
		slog.Debug("Using existing ACME account registration", slog.String("email", m.cfg.Email))
	}

	return client, nil
}

func needRenew(now, notAfter time.Time, renewBefore time.Duration) bool {
	return now.Add(renewBefore).After(notAfter)
}
