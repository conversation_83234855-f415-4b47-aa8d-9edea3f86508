package main

import (
	"crypto/sha256"
	"fmt"
	"log"
	"log/slog"
	"net/http"
	"os"
	"strings"
	"time"

	"code.ixdev.cn/cnix/cbdv/acme-relay/acmex"
	"gopkg.in/yaml.v3"
)

// Config 配置结构体
type Config struct {
	Server struct {
		ListenAddr        string `yaml:"listen_addr"`
		ReadHeaderTimeout int    `yaml:"read_header_timeout"`
	} `yaml:"server"`
	ACME struct {
		AccountEmail    string `yaml:"account_email"`
		DataDir         string `yaml:"data_dir"`
		DirectoryURL    string `yaml:"directory_url"`
		RenewBeforeDays int    `yaml:"renew_before_days"`
		KeyType         string `yaml:"key_type"`
	} `yaml:"acme"`
	DNS struct {
		TencentCloud struct {
			SecretID  string `yaml:"secret_id"`
			SecretKey string `yaml:"secret_key"`
		} `yaml:"tencentcloud"`
		RecursiveNameservers []string `yaml:"recursive_nameservers"`
	} `yaml:"dns"`
	Log struct {
		Level  string `yaml:"level"`  // debug, info, warn, error
		Format string `yaml:"format"` // json, text
	} `yaml:"log"`
}

// loadConfig 从配置文件加载配置
func loadConfig(configPath string) (*Config, error) {
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, err
	}

	var cfg Config
	if err := yaml.Unmarshal(data, &cfg); err != nil {
		return nil, err
	}

	return &cfg, nil
}

// initLogger 初始化结构化日志记录器
func initLogger(cfg *Config) *slog.Logger {
	var level slog.Level
	switch strings.ToLower(cfg.Log.Level) {
	case "debug":
		level = slog.LevelDebug
	case "info":
		level = slog.LevelInfo
	case "warn", "warning":
		level = slog.LevelWarn
	case "error":
		level = slog.LevelError
	default:
		level = slog.LevelInfo
	}

	var handler slog.Handler
	opts := &slog.HandlerOptions{Level: level}

	if strings.ToLower(cfg.Log.Format) == "json" {
		handler = slog.NewJSONHandler(os.Stdout, opts)
	} else {
		handler = slog.NewTextHandler(os.Stdout, opts)
	}

	return slog.New(handler)
}

// hashString 计算字符串的 SHA256 哈希值（用于日志中的敏感信息脱敏）
func hashString(s string) string {
	if s == "" {
		return ""
	}
	h := sha256.Sum256([]byte(s))
	return fmt.Sprintf("sha256:%x", h[:8]) // 只显示前8字节
}

// getClientIP 获取客户端IP地址
func getClientIP(r *http.Request) string {
	// 优先从 X-Forwarded-For 获取（如果通过代理）
	if xff := r.Header.Get("X-Forwarded-For"); xff != "" {
		ips := strings.Split(xff, ",")
		if len(ips) > 0 {
			return strings.TrimSpace(ips[0])
		}
	}

	// 从 X-Real-IP 获取
	if xri := r.Header.Get("X-Real-IP"); xri != "" {
		return strings.TrimSpace(xri)
	}

	// 最后从 RemoteAddr 获取
	if idx := strings.LastIndex(r.RemoteAddr, ":"); idx != -1 {
		return r.RemoteAddr[:idx]
	}
	return r.RemoteAddr
}

func main() {
	// 加载配置文件
	configPath := getEnv("CONFIG_PATH", "conf.yaml")
	config, err := loadConfig(configPath)
	if err != nil {
		log.Fatalf("load config: %v", err)
	}

	// 初始化结构化日志记录器
	logger := initLogger(config)
	slog.SetDefault(logger)

	// 设置腾讯云认证环境变量
	if config.DNS.TencentCloud.SecretID != "" {
		os.Setenv("TENCENTCLOUD_SECRET_ID", config.DNS.TencentCloud.SecretID)
	}
	if config.DNS.TencentCloud.SecretKey != "" {
		os.Setenv("TENCENTCLOUD_SECRET_KEY", config.DNS.TencentCloud.SecretKey)
	}

	// 创建 ACME 配置
	acmeCfg := acmex.DefaultConfig(config.ACME.AccountEmail, config.ACME.DataDir)
	if config.ACME.DirectoryURL != "" {
		acmeCfg.CADirURL = config.ACME.DirectoryURL
	}
	if config.ACME.RenewBeforeDays > 0 {
		acmeCfg.RenewBefore = time.Duration(config.ACME.RenewBeforeDays) * 24 * time.Hour
	}

	mgr, err := acmex.NewManager(acmeCfg)
	if err != nil {
		logger.Error("Failed to initialize ACME manager", slog.String("error", err.Error()))
		log.Fatalf("init manager: %v", err)
	}

	logger.Info("ACME manager initialized successfully")

	mux := http.NewServeMux()
	mux.HandleFunc("/getcert", func(w http.ResponseWriter, r *http.Request) {
		startTime := time.Now()
		clientIP := getClientIP(r)
		serverName := r.URL.Query().Get("server_name")

		// 记录请求开始
		logger.Info("Certificate request received",
			slog.String("client_ip", clientIP),
			slog.String("method", r.Method),
			slog.String("user_agent", r.UserAgent()),
			slog.String("server_name", serverName),
			slog.String("request_id", fmt.Sprintf("%d", startTime.UnixNano())))

		if serverName == "" {
			logger.Warn("Certificate request rejected: missing server_name",
				slog.String("client_ip", clientIP),
				slog.String("method", r.Method))
			http.Error(w, "missing server_name", http.StatusBadRequest)
			return
		}

		// 生产里你可以校验来源（只允许内网/Caddy），或做域名白名单检查
		ctx := r.Context()

		logger.Info("Starting certificate issuance/renewal",
			slog.String("server_name", serverName),
			slog.String("client_ip", clientIP))

		fullchain, key, err := mgr.GetCertificate(ctx, serverName)
		duration := time.Since(startTime)

		if err != nil {
			logger.Error("Certificate issuance/renewal failed",
				slog.String("server_name", serverName),
				slog.String("client_ip", clientIP),
				slog.String("error", err.Error()),
				slog.Duration("duration", duration))
			http.Error(w, "issue/renew failed", http.StatusInternalServerError)
			return
		}

		// 记录成功响应（不记录证书和私钥内容）
		logger.Info("Certificate issued/renewed successfully",
			slog.String("server_name", serverName),
			slog.String("client_ip", clientIP),
			slog.Duration("duration", duration),
			slog.Int("fullchain_size", len(fullchain)),
			slog.Int("key_size", len(key)))

		w.Header().Set("Content-Type", "application/x-pem-file")
		w.Header().Set("Cache-Control", "no-store, no-cache, must-revalidate")
		w.Header().Set("Pragma", "no-cache")
		w.WriteHeader(http.StatusOK)
		_, _ = w.Write(fullchain)
		_, _ = w.Write([]byte("\n"))
		_, _ = w.Write(key)
	})

	srv := &http.Server{
		Addr:              config.Server.ListenAddr,
		Handler:           mux,
		ReadHeaderTimeout: time.Duration(config.Server.ReadHeaderTimeout) * time.Second,
	}

	logger.Info("ACME Relay server starting",
		slog.String("listen_addr", config.Server.ListenAddr),
		slog.Duration("read_header_timeout", srv.ReadHeaderTimeout),
		slog.String("endpoint", fmt.Sprintf("http://%s/getcert", config.Server.ListenAddr)))

	if err := srv.ListenAndServe(); err != nil {
		logger.Error("Server failed to start", slog.String("error", err.Error()))
		log.Fatal(err)
	}
}

func getEnv(k, def string) string {
	if v := os.Getenv(k); v != "" {
		return v
	}
	return def
}
