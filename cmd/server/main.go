package main

import (
	"log"
	"net/http"
	"os"
	"time"

	"code.ixdev.cn/cnix/cbdv/acme-relay/acmex"
	"gopkg.in/yaml.v3"
)

// Config 配置结构体
type Config struct {
	Server struct {
		ListenAddr        string `yaml:"listen_addr"`
		ReadHeaderTimeout int    `yaml:"read_header_timeout"`
	} `yaml:"server"`
	ACME struct {
		AccountEmail    string `yaml:"account_email"`
		DataDir         string `yaml:"data_dir"`
		DirectoryURL    string `yaml:"directory_url"`
		RenewBeforeDays int    `yaml:"renew_before_days"`
		KeyType         string `yaml:"key_type"`
	} `yaml:"acme"`
	DNS struct {
		TencentCloud struct {
			SecretID  string `yaml:"secret_id"`
			SecretKey string `yaml:"secret_key"`
		} `yaml:"tencentcloud"`
		RecursiveNameservers []string `yaml:"recursive_nameservers"`
	} `yaml:"dns"`
}

// loadConfig 从配置文件加载配置
func loadConfig(configPath string) (*Config, error) {
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, err
	}

	var cfg Config
	if err := yaml.Unmarshal(data, &cfg); err != nil {
		return nil, err
	}

	return &cfg, nil
}

func main() {
	// 加载配置文件
	configPath := getEnv("CONFIG_PATH", "conf.yaml")
	config, err := loadConfig(configPath)
	if err != nil {
		log.Fatalf("load config: %v", err)
	}

	// 设置腾讯云认证环境变量
	if config.DNS.TencentCloud.SecretID != "" {
		os.Setenv("TENCENTCLOUD_SECRET_ID", config.DNS.TencentCloud.SecretID)
	}
	if config.DNS.TencentCloud.SecretKey != "" {
		os.Setenv("TENCENTCLOUD_SECRET_KEY", config.DNS.TencentCloud.SecretKey)
	}

	// 创建 ACME 配置
	acmeCfg := acmex.DefaultConfig(config.ACME.AccountEmail, config.ACME.DataDir)
	if config.ACME.DirectoryURL != "" {
		acmeCfg.CADirURL = config.ACME.DirectoryURL
	}
	if config.ACME.RenewBeforeDays > 0 {
		acmeCfg.RenewBefore = time.Duration(config.ACME.RenewBeforeDays) * 24 * time.Hour
	}

	mgr, err := acmex.NewManager(acmeCfg)
	if err != nil {
		log.Fatalf("init manager: %v", err)
	}

	mux := http.NewServeMux()
	mux.HandleFunc("/getcert", func(w http.ResponseWriter, r *http.Request) {
		serverName := r.URL.Query().Get("server_name")
		if serverName == "" {
			http.Error(w, "missing server_name", http.StatusBadRequest)
			return
		}

		// 生产里你可以校验来源（只允许内网/Caddy），或做域名白名单检查
		ctx := r.Context()
		fullchain, key, err := mgr.GetCertificate(ctx, serverName)
		if err != nil {
			log.Printf("[ERR] issue %s: %v", serverName, err)
			http.Error(w, "issue/renew failed", http.StatusInternalServerError)
			return
		}

		w.Header().Set("Content-Type", "application/x-pem-file")
		w.WriteHeader(http.StatusOK)
		_, _ = w.Write(fullchain)
		_, _ = w.Write([]byte("\n"))
		_, _ = w.Write(key)
	})

	srv := &http.Server{
		Addr:              config.Server.ListenAddr,
		Handler:           mux,
		ReadHeaderTimeout: time.Duration(config.Server.ReadHeaderTimeout) * time.Second,
	}
	log.Printf("certbox listening on http://%s/getcert", config.Server.ListenAddr)
	log.Fatal(srv.ListenAndServe())
}

func getEnv(k, def string) string {
	if v := os.Getenv(k); v != "" {
		return v
	}
	return def
}
