package main

import (
	"fmt"
	"log"
	"log/slog"
	"net/http"
	"os"
	"time"

	"code.ixdev.cn/cnix/cbdv/acme-relay/acmex"
	"code.ixdev.cn/cnix/cbdv/acme-relay/internal/config"
	"code.ixdev.cn/cnix/cbdv/acme-relay/internal/handlers"
	"code.ixdev.cn/cnix/cbdv/acme-relay/internal/logger"
)

func main() {
	// 加载配置文件
	configPath := config.GetEnv("CONFIG_PATH", "conf.yaml")
	cfg, err := config.Load(configPath)
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化结构化日志记录器
	appLogger := logger.Init(cfg)
	slog.SetDefault(appLogger)

	// 设置腾讯云认证环境变量
	if cfg.DNS.TencentCloud.SecretID != "" {
		os.Setenv("TENCENTCLOUD_SECRET_ID", cfg.DNS.TencentCloud.SecretID)
	}
	if cfg.DNS.TencentCloud.SecretKey != "" {
		os.Setenv("TENCENTCLOUD_SECRET_KEY", cfg.DNS.TencentCloud.SecretKey)
	}

	// 创建 ACME 配置
	acmeCfg := acmex.DefaultConfig(cfg.ACME.AccountEmail, cfg.ACME.DataDir)
	if cfg.ACME.DirectoryURL != "" {
		acmeCfg.CADirURL = cfg.ACME.DirectoryURL
	}
	if cfg.ACME.RenewBeforeDays > 0 {
		acmeCfg.RenewBefore = time.Duration(cfg.ACME.RenewBeforeDays) * 24 * time.Hour
	}

	mgr, err := acmex.NewManager(acmeCfg)
	if err != nil {
		appLogger.Error("Failed to initialize ACME manager", slog.String("error", err.Error()))
		log.Fatalf("Failed to initialize ACME manager: %v", err)
	}

	appLogger.Info("ACME manager initialized successfully")

	// 创建证书处理器
	certHandler := handlers.NewCertHandler(cfg, mgr, appLogger)

	// 设置路由
	mux := http.NewServeMux()
	mux.HandleFunc("/getcert", certHandler.GetCert)

	// 创建 HTTP 服务器
	srv := &http.Server{
		Addr:              cfg.Server.ListenAddr,
		Handler:           mux,
		ReadHeaderTimeout: time.Duration(cfg.Server.ReadHeaderTimeout) * time.Second,
	}

	appLogger.Info("ACME Relay server starting",
		slog.String("listen_addr", cfg.Server.ListenAddr),
		slog.Duration("read_header_timeout", srv.ReadHeaderTimeout),
		slog.String("endpoint", fmt.Sprintf("http://%s/getcert", cfg.Server.ListenAddr)))

	if err := srv.ListenAndServe(); err != nil {
		appLogger.Error("Server failed to start", slog.String("error", err.Error()))
		log.Fatal(err)
	}
}
