package main

import (
	"fmt"
	"log"
	"log/slog"
	"net"
	"net/http"
	"os"
	"strings"
	"time"

	"code.ixdev.cn/cnix/cbdv/acme-relay/acmex"
	"gopkg.in/yaml.v3"
)

// Config 配置结构体
type Config struct {
	Server struct {
		ListenAddr        string `yaml:"listen_addr"`
		ReadHeaderTimeout int    `yaml:"read_header_timeout"`
	} `yaml:"server"`
	ACME struct {
		AccountEmail    string `yaml:"account_email"`
		DataDir         string `yaml:"data_dir"`
		DirectoryURL    string `yaml:"directory_url"`
		RenewBeforeDays int    `yaml:"renew_before_days"`
		KeyType         string `yaml:"key_type"`
	} `yaml:"acme"`
	DNS struct {
		TencentCloud struct {
			SecretID  string `yaml:"secret_id"`
			SecretKey string `yaml:"secret_key"`
		} `yaml:"tencentcloud"`
		RecursiveNameservers []string `yaml:"recursive_nameservers"`
	} `yaml:"dns"`
	Log struct {
		Level  string `yaml:"level"`  // debug, info, warn, error
		Format string `yaml:"format"` // json, text
	} `yaml:"log"`
	Security struct {
		AllowedIPs     []string `yaml:"allowed_ips"`     // IP 白名单
		AllowedCIDRs   []string `yaml:"allowed_cidrs"`   // CIDR 白名单
		AllowedDomains []string `yaml:"allowed_domains"` // 域名白名单
	} `yaml:"security"`
}

// loadConfig 从配置文件加载配置
func loadConfig(configPath string) (*Config, error) {
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, err
	}

	var cfg Config
	if err := yaml.Unmarshal(data, &cfg); err != nil {
		return nil, err
	}

	return &cfg, nil
}

// initLogger 初始化结构化日志记录器
func initLogger(cfg *Config) *slog.Logger {
	var level slog.Level
	switch strings.ToLower(cfg.Log.Level) {
	case "debug":
		level = slog.LevelDebug
	case "info":
		level = slog.LevelInfo
	case "warn", "warning":
		level = slog.LevelWarn
	case "error":
		level = slog.LevelError
	default:
		level = slog.LevelInfo
	}

	var handler slog.Handler
	opts := &slog.HandlerOptions{Level: level}

	if strings.ToLower(cfg.Log.Format) == "json" {
		handler = slog.NewJSONHandler(os.Stdout, opts)
	} else {
		handler = slog.NewTextHandler(os.Stdout, opts)
	}

	return slog.New(handler)
}

// getClientIP 获取客户端IP地址
func getClientIP(r *http.Request) string {
	// 优先从 X-Forwarded-For 获取（如果通过代理）
	if xff := r.Header.Get("X-Forwarded-For"); xff != "" {
		ips := strings.Split(xff, ",")
		if len(ips) > 0 {
			return strings.TrimSpace(ips[0])
		}
	}

	// 从 X-Real-IP 获取
	if xri := r.Header.Get("X-Real-IP"); xri != "" {
		return strings.TrimSpace(xri)
	}

	// 最后从 RemoteAddr 获取
	if idx := strings.LastIndex(r.RemoteAddr, ":"); idx != -1 {
		return r.RemoteAddr[:idx]
	}
	return r.RemoteAddr
}

// isIPAllowed 检查 IP 是否在白名单中
func isIPAllowed(clientIP string, allowedIPs []string, allowedCIDRs []string) bool {
	// 如果没有配置白名单，则允许所有 IP
	if len(allowedIPs) == 0 && len(allowedCIDRs) == 0 {
		return true
	}

	ip := net.ParseIP(clientIP)
	if ip == nil {
		return false
	}

	// 检查精确 IP 匹配
	for _, allowedIP := range allowedIPs {
		if allowedIP == clientIP {
			return true
		}
	}

	// 检查 CIDR 匹配
	for _, cidr := range allowedCIDRs {
		_, network, err := net.ParseCIDR(cidr)
		if err != nil {
			continue
		}
		if network.Contains(ip) {
			return true
		}
	}

	return false
}

// isDomainAllowed 检查域名是否在白名单中
func isDomainAllowed(domain string, allowedDomains []string) bool {
	// 如果没有配置域名白名单，则允许所有域名
	if len(allowedDomains) == 0 {
		return true
	}

	domain = strings.ToLower(strings.TrimSpace(domain))

	for _, allowed := range allowedDomains {
		allowed = strings.ToLower(strings.TrimSpace(allowed))

		// 精确匹配
		if domain == allowed {
			return true
		}

		// 通配符匹配（如 *.example.com）
		if strings.HasPrefix(allowed, "*.") {
			suffix := allowed[2:] // 去掉 "*."
			if strings.HasSuffix(domain, "."+suffix) || domain == suffix {
				return true
			}
		}

		// 后缀匹配（如 .example.com）
		if strings.HasPrefix(allowed, ".") {
			if strings.HasSuffix(domain, allowed) {
				return true
			}
		}
	}

	return false
}

func main() {
	// 加载配置文件
	configPath := getEnv("CONFIG_PATH", "conf.yaml")
	config, err := loadConfig(configPath)
	if err != nil {
		log.Fatalf("load config: %v", err)
	}

	// 初始化结构化日志记录器
	logger := initLogger(config)
	slog.SetDefault(logger)

	// 记录安全配置
	logger.Info("Security configuration loaded",
		slog.Int("allowed_ips_count", len(config.Security.AllowedIPs)),
		slog.Int("allowed_cidrs_count", len(config.Security.AllowedCIDRs)),
		slog.Int("allowed_domains_count", len(config.Security.AllowedDomains)),
		slog.Any("allowed_ips", config.Security.AllowedIPs),
		slog.Any("allowed_cidrs", config.Security.AllowedCIDRs),
		slog.Any("allowed_domains", config.Security.AllowedDomains))

	// 设置腾讯云认证环境变量
	if config.DNS.TencentCloud.SecretID != "" {
		os.Setenv("TENCENTCLOUD_SECRET_ID", config.DNS.TencentCloud.SecretID)
	}
	if config.DNS.TencentCloud.SecretKey != "" {
		os.Setenv("TENCENTCLOUD_SECRET_KEY", config.DNS.TencentCloud.SecretKey)
	}

	// 创建 ACME 配置
	acmeCfg := acmex.DefaultConfig(config.ACME.AccountEmail, config.ACME.DataDir)
	if config.ACME.DirectoryURL != "" {
		acmeCfg.CADirURL = config.ACME.DirectoryURL
	}
	if config.ACME.RenewBeforeDays > 0 {
		acmeCfg.RenewBefore = time.Duration(config.ACME.RenewBeforeDays) * 24 * time.Hour
	}

	mgr, err := acmex.NewManager(acmeCfg)
	if err != nil {
		logger.Error("Failed to initialize ACME manager", slog.String("error", err.Error()))
		log.Fatalf("init manager: %v", err)
	}

	logger.Info("ACME manager initialized successfully")

	mux := http.NewServeMux()
	mux.HandleFunc("/getcert", func(w http.ResponseWriter, r *http.Request) {
		startTime := time.Now()
		clientIP := getClientIP(r)
		serverName := r.URL.Query().Get("server_name")

		// 记录请求开始
		logger.Info("Certificate request received",
			slog.String("client_ip", clientIP),
			slog.String("method", r.Method),
			slog.String("user_agent", r.UserAgent()),
			slog.String("server_name", serverName),
			slog.String("request_id", fmt.Sprintf("%d", startTime.UnixNano())))

		// 检查 IP 白名单
		if !isIPAllowed(clientIP, config.Security.AllowedIPs, config.Security.AllowedCIDRs) {
			logger.Warn("Certificate request rejected: IP not in whitelist",
				slog.String("client_ip", clientIP),
				slog.String("server_name", serverName),
				slog.Any("allowed_ips", config.Security.AllowedIPs),
				slog.Any("allowed_cidrs", config.Security.AllowedCIDRs))
			http.Error(w, "access denied", http.StatusForbidden)
			return
		}

		if serverName == "" {
			logger.Warn("Certificate request rejected: missing server_name",
				slog.String("client_ip", clientIP),
				slog.String("method", r.Method))
			http.Error(w, "missing server_name", http.StatusBadRequest)
			return
		}

		// 检查域名白名单
		if !isDomainAllowed(serverName, config.Security.AllowedDomains) {
			logger.Warn("Certificate request rejected: domain not in whitelist",
				slog.String("client_ip", clientIP),
				slog.String("server_name", serverName),
				slog.Any("allowed_domains", config.Security.AllowedDomains))
			http.Error(w, "domain not allowed", http.StatusForbidden)
			return
		}

		// 生产里你可以校验来源（只允许内网/Caddy），或做域名白名单检查
		ctx := r.Context()

		logger.Info("Starting certificate issuance/renewal",
			slog.String("server_name", serverName),
			slog.String("client_ip", clientIP))

		fullchain, key, err := mgr.GetCertificate(ctx, serverName)
		duration := time.Since(startTime)

		if err != nil {
			logger.Error("Certificate issuance/renewal failed",
				slog.String("server_name", serverName),
				slog.String("client_ip", clientIP),
				slog.String("error", err.Error()),
				slog.Duration("duration", duration))
			http.Error(w, "issue/renew failed", http.StatusInternalServerError)
			return
		}

		// 记录成功响应（不记录证书和私钥内容）
		logger.Info("Certificate issued/renewed successfully",
			slog.String("server_name", serverName),
			slog.String("client_ip", clientIP),
			slog.Duration("duration", duration),
			slog.Int("fullchain_size", len(fullchain)),
			slog.Int("key_size", len(key)))

		w.Header().Set("Content-Type", "application/x-pem-file")
		w.Header().Set("Cache-Control", "no-store, no-cache, must-revalidate")
		w.Header().Set("Pragma", "no-cache")
		w.WriteHeader(http.StatusOK)
		_, _ = w.Write(fullchain)
		_, _ = w.Write([]byte("\n"))
		_, _ = w.Write(key)
	})

	srv := &http.Server{
		Addr:              config.Server.ListenAddr,
		Handler:           mux,
		ReadHeaderTimeout: time.Duration(config.Server.ReadHeaderTimeout) * time.Second,
	}

	logger.Info("ACME Relay server starting",
		slog.String("listen_addr", config.Server.ListenAddr),
		slog.Duration("read_header_timeout", srv.ReadHeaderTimeout),
		slog.String("endpoint", fmt.Sprintf("http://%s/getcert", config.Server.ListenAddr)))

	if err := srv.ListenAndServe(); err != nil {
		logger.Error("Server failed to start", slog.String("error", err.Error()))
		log.Fatal(err)
	}
}

func getEnv(k, def string) string {
	if v := os.Getenv(k); v != "" {
		return v
	}
	return def
}
