# ACME Relay

一个用于 Caddy 服务器的 ACME 证书中继服务，支持通过腾讯云 DNS 进行 DNS-01 验证。

## 功能特性

- 🔒 **安全的证书获取**: 通过 ACME DNS-01 验证自动获取和续期 SSL/TLS 证书
- 🌐 **腾讯云 DNS 集成**: 支持腾讯云 DNS 服务进行域名验证
- 🛡️ **多层安全防护**:
  - IP 白名单（支持精确 IP 和 CIDR 网段）
  - 域名白名单（支持精确匹配、通配符和后缀匹配）
  - 结构化日志记录和审计
- 📊 **详细日志记录**: 记录所有关键操作和安全事件
- ⚡ **高性能**: 本地证书缓存，避免重复申请

## 安全特性

### IP 白名单
- **精确 IP 匹配**: 允许特定 IP 地址访问
- **CIDR 网段匹配**: 支持网段级别的访问控制
- **默认行为**: 如果未配置白名单，则允许所有 IP 访问

### 域名白名单
- **精确匹配**: `example.com` 只匹配该域名
- **通配符匹配**: `*.example.com` 匹配所有子域名
- **后缀匹配**: `.example.com` 匹配该域名及所有子域名
- **默认行为**: 如果未配置白名单，则允许所有域名

## Caddy 集成

本服务专为 Caddy 服务器设计，支持 `get_certificate` 指令：

```
get_certificate http <url>
```

Caddy 会向指定 URL 发送请求，URL 包含以下查询参数：
- `server_name`: SNI 值（域名）
- `signature_schemes`: 签名算法列表
- `cipher_suites`: 密码套件列表

响应必须包含完整的 PEM 证书链和私钥。
