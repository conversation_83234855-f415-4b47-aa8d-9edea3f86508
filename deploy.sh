#!/bin/bash

# ACME Relay 部署脚本
# 用于安全部署 ACME 中继服务

set -e

# 配置变量
SERVICE_NAME="acme-relay"
SERVICE_USER="acme-relay"
SERVICE_DIR="/opt/acme-relay"
CONFIG_FILE="$SERVICE_DIR/conf.yaml"
DATA_DIR="$SERVICE_DIR/data"
LOG_DIR="/var/log/acme-relay"

echo "🚀 开始部署 ACME Relay 服务..."

# 检查是否以 root 权限运行
if [[ $EUID -ne 0 ]]; then
   echo "❌ 此脚本需要 root 权限运行"
   exit 1
fi

# 创建服务用户
if ! id "$SERVICE_USER" &>/dev/null; then
    echo "📝 创建服务用户: $SERVICE_USER"
    useradd --system --no-create-home --shell /bin/false "$SERVICE_USER"
fi

# 创建目录
echo "📁 创建服务目录..."
mkdir -p "$SERVICE_DIR" "$DATA_DIR" "$LOG_DIR"

# 编译服务
echo "🔨 编译服务..."
go build -o "$SERVICE_DIR/$SERVICE_NAME" cmd/server/main.go

# 复制配置文件模板
if [[ ! -f "$CONFIG_FILE" ]]; then
    echo "📋 复制配置文件模板..."
    cp conf.yaml.example "$CONFIG_FILE"
    echo "⚠️  请编辑 $CONFIG_FILE 配置文件"
fi

# 设置权限
echo "🔒 设置文件权限..."
chown -R "$SERVICE_USER:$SERVICE_USER" "$SERVICE_DIR" "$LOG_DIR"
chmod 755 "$SERVICE_DIR"
chmod 700 "$DATA_DIR"  # 数据目录只有服务用户可访问
chmod 600 "$CONFIG_FILE"  # 配置文件只有服务用户可读写
chmod 755 "$SERVICE_DIR/$SERVICE_NAME"

# 创建 systemd 服务文件
echo "⚙️  创建 systemd 服务..."
cat > "/etc/systemd/system/$SERVICE_NAME.service" << EOF
[Unit]
Description=ACME Relay Service
After=network.target
Wants=network.target

[Service]
Type=simple
User=$SERVICE_USER
Group=$SERVICE_USER
WorkingDirectory=$SERVICE_DIR
ExecStart=$SERVICE_DIR/$SERVICE_NAME
Environment=CONFIG_PATH=$CONFIG_FILE
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=$SERVICE_NAME

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=$DATA_DIR $LOG_DIR
ProtectHome=true
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true

[Install]
WantedBy=multi-user.target
EOF

# 重新加载 systemd
systemctl daemon-reload

echo "✅ 部署完成！"
echo ""
echo "📝 下一步操作："
echo "1. 编辑配置文件: $CONFIG_FILE"
echo "2. 启动服务: systemctl start $SERVICE_NAME"
echo "3. 启用开机自启: systemctl enable $SERVICE_NAME"
echo "4. 查看服务状态: systemctl status $SERVICE_NAME"
echo "5. 查看日志: journalctl -u $SERVICE_NAME -f"
echo ""
echo "🔒 安全提醒："
echo "- 确保配置了 IP 白名单"
echo "- 确保配置了域名白名单"
echo "- 定期轮换腾讯云 API 密钥"
echo "- 监控服务日志"
